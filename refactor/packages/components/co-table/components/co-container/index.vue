<!--
  Co-Container 容器组件

  功能特性：
  - 智能表单容器切换
  - 根据是否包含表单项动态选择容器类型
  - 支持表单验证功能
  - 提供表单引用访问

  使用场景：
  - 表格内容容器
  - 表单项包装容器
  - 需要表单验证的场景
-->
<template>
	<!--
		条件渲染：如果包含表单项，使用 el-form 容器
		- v-if="hasFormItem": 根据是否有表单项决定是否渲染表单容器
		- class="zs-table-content": 应用表格内容样式
		- ref="tFormRef": 表单引用，用于访问表单验证方法
		- :model="model": 绑定表单数据模型
		- :size="configOpts.size": 设置表单组件尺寸
		- :validate-on-rule-change="false": 禁用规则变化时的自动验证
	-->
	<el-form
		v-if="hasFormItem"
		class="zs-table-content"
		ref="tFormRef"
		:model="model"
		:size="configOpts.size"
		:validate-on-rule-change="false"
	>
		<!-- 插槽：渲染表格内容或表单项 -->
		<slot />
	</el-form>

	<!--
		条件渲染：如果不包含表单项，使用普通模板容器
		避免不必要的表单包装，提高性能
	-->
	<template v-else>
		<!-- 插槽：直接渲染内容，不包装表单 -->
		<slot />
	</template>
</template>

<script>
/**
 * Co-Container 容器组件
 *
 * 智能容器组件，根据内容类型自动选择合适的容器
 * 当包含表单项时使用 el-form 容器，否则使用普通模板
 *
 * 主要功能：
 * - 智能容器类型选择
 * - 表单验证支持
 * - 表单引用提供
 * - 性能优化（避免不必要的表单包装）
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

// 导入 Vue 3 的 defineComponent 函数
import { defineComponent } from 'vue';

export default defineComponent({
	name: 'CoContainer',

	// 不继承父组件的属性
	inheritAttrs: false,

	/**
	 * 组件属性定义
	 */
	props: {
		// 表单数据模型，用于表单验证和数据绑定
		model: Object,

		// 是否包含表单项，决定使用哪种容器类型
		hasFormItem: Boolean,

		// 配置选项对象，包含尺寸等配置
		configOpts: Object,
	},

	methods: {
		/**
		 * 获取表单引用
		 * 提供对内部 el-form 组件的访问，用于表单验证等操作
		 *
		 * @returns {Object|undefined} 表单组件引用，如果不是表单容器则返回 undefined
		 *
		 * @example
		 * // 在父组件中访问表单引用
		 * const formRef = this.$refs.container.formRef();
		 * if (formRef) {
		 *   formRef.validate((valid) => {
		 *     console.log('表单验证结果:', valid);
		 *   });
		 * }
		 */
		formRef() {
			return this.$refs.tFormRef;
		},
	},
});
</script>
